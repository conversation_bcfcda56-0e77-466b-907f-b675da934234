# 生成RK3588 Qwen3-4B评测计划的提示词

## 简单易懂的提示词

```
请帮我写一个RK3588开发板测试Qwen3-4B人工智能模型的完整计划，要求：

1. 计划分3个阶段：
- 第一阶段：准备环境和安装模型
- 第二阶段：启动服务和测试智商
- 第三阶段：优化性能

2. 第二阶段要详细写出：
- 怎么启动AI服务
- 怎么安装测试系统（要有6个文件：数据管理、API连接、快速测试、完整评测、性能监控、主程序）
- 怎么下载和运行测试
- 怎么检查结果
- 完整的操作步骤

3. 要测试10种智商能力，每种都要写清楚：
- OpenBookQA：科学常识（标准分0.382）
- TriviaQA：知识问答（标准分0.508）
- HellaSwag：常识推理（标准分0.555）
- SQuAD2：阅读理解（标准分0.588）
- XWINO：语言理解（标准分0.891）
- MMLU：学科知识（标准分0.729）
- GSM8K：数学计算（标准分0.719）
- MATH：高难数学（标准分0.520）
- BBH：逻辑推理（标准分0.594）
- HumanEval：编程能力（标准分0.617）

每个测试要写：测什么能力、什么题型、从哪下载、怎么加载

4. 性能要求：
- 反应速度：小于0.5秒
- 生成速度：每秒15个字以上
- 智商保持：95%以上不能降低
- 内存占用：小于2GB
- CPU使用：小于30%

5. 使用说明要包括：
- 需要什么设备（RK3588开发板、8GB内存、15GB硬盘）
- 怎么安装软件
- 怎么运行命令（下载、快速测试、完整测试、性能测试）
- 结果文件在哪里
- 出问题怎么解决

6. 要有完整的操作命令，从开始到结束6个步骤，每步都能直接复制运行

输出：完整的说明文档，用中文写，步骤清楚，普通人能看懂
```

## 怎么使用

1. 复制上面的提示词
2. 粘贴给AI助手
3. 就能得到完整的测试计划

## 这个提示词的好处

- **用普通话写的** - 不用懂编程也能看懂
- **步骤很清楚** - 从头到尾怎么做都写了
- **能直接用** - 所有命令都能复制运行
- **结果明确** - 知道测试通过还是失败

---
创建时间: 2025年8月2日
用途: 让AI帮你写RK3588测试AI模型的完整计划
