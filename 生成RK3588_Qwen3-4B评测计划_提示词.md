# 生成RK3588 Qwen3-4B评测计划的提示词

## 完整提示词

```
请生成RK3588平台Qwen3-4B模型的完整评测计划，要求：

1. 计划结构：
- 阶段一：环境准备和模型部署
- 阶段二：Flask服务启动与智商考试评测系统部署  
- 阶段三：NPU优化实施

2. 第二阶段必须包含：
- 2.1 Flask服务启动配置
- 2.2 智商考试系统部署（包含6个核心Python模块：evaluation_datasets.py、api_client.py、quick_test.py、qwen3_4b_evaluation.py、performance_monitor.py、run_evaluation.py）
- 2.3 考试执行与评分（包含具体bash命令）
- 2.4 服务集成测试与基准建立
- 2.5 完整评测流程（一键执行）

3. 10个智商测试数据集详细信息：
OpenBookQA(0.382)、TriviaQA(0.508)、HellaSwag(0.555)、SQuAD2(0.588)、XWINO(0.891)、MMLU(0.729)、GSM8K(0.719)、MATH(0.520)、BBH(0.594)、HumanEval(0.617)

每个数据集必须包含：
- 基准分数
- 评测能力描述
- 题目类型
- Hugging Face数据集地址
- Python加载方式代码
- 备选地址（如果有）

4. 性能指标标准：
- 首Token延迟<500ms
- Token生成速度>15tokens/s  
- 智商保持率>95%
- 模型内存占用<2GB
- CPU利用率<30%

5. 包含完整的系统使用说明：
- 环境要求（Python 3.8+、RK3588、麒麟v10 sp1、8GB内存、15GB存储）
- 安装配置命令
- 运行命令（download、quick、full、performance模式）
- 性能指标标准表格
- 输出文件说明（results/、reports/、logs/）
- 故障排除指南

6. 数据集配置信息：
```python
datasets_info = {
    "openbookqa": "allenai/openbookqa",
    "triviaqa": "mandarjoshi/trivia_qa", 
    "hellaswag": "Rowan/hellaswag",
    "squad_v2": "rajpurkar/squad_v2",
    "mmlu": "cais/mmlu",
    "gsm8k": "openai/gsm8k",
    "math": "hendrycks/competition_math",
    "bbh": "lukaemon/bbh",
    "humaneval": "openai/openai_humaneval"
}
```

7. 一键执行流程：
```bash
# 第1步：启动Flask服务
python flask_server.py --rkllm_model_path /path/to/qwen3-4b.rkllm

# 第2步：安装依赖并验证系统
pip install -r requirements.txt
python test_system.py

# 第3步：下载数据集
python run_evaluation.py --mode download

# 第4步：快速验证
python run_evaluation.py --mode quick

# 第5步：完整评测
python run_evaluation.py --mode full --include-performance

# 第6步：查看报告
ls reports/evaluation_report_*.md
```

8. 所有bash命令必须可直接执行，所有Python模块名称要具体明确。

输出格式：完整的Markdown文档，包含所有技术细节和使用说明，结构清晰，内容准确。
```

## 使用说明

将上述提示词复制粘贴给AI助手，即可生成完整的RK3588 Qwen3-4B评测计划文档。

## 关键要素

1. **准确的数据集信息** - 10个数据集的完整信息
2. **具体的模块名称** - 6个Python文件的准确命名
3. **可执行的命令** - 所有bash命令都可直接运行
4. **明确的性能标准** - 具体的数值指标
5. **完整的使用流程** - 从安装到执行的全过程

---
创建时间: 2025-08-02
适用场景: RK3588平台AI模型评测计划生成
