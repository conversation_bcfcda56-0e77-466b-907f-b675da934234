# RK3588 + Qwen3-4B 智商评测系统

基于RK3588平台的Qwen3-4B模型智商能力评测系统，支持10个标准数据集的完整评测和性能监控。

## 项目概述

本评测系统根据 `RK3588_Qwen3-4B_优化计划.md` 设计，用于评估RK3588平台上Qwen3-4B模型的智商表现和性能指标。

### 核心功能

- **10项智商测试**: 支持OpenBookQA、TriviaQA、HellaSwag、SQuAD2、XWINO、MMLU、GSM8K、MATH、BBH、HumanEval
- **性能监控**: 监控延迟、吞吐量、内存占用、功耗等关键指标
- **自动化评测**: 支持快速验证和完整评测两种模式
- **报告生成**: 自动生成Markdown格式的详细评测报告
- **Flask集成**: 与现有的flask_server.py完美配套

## 系统架构

```
RK3588 Qwen3-4B 评测系统
├── evaluation_datasets.py    # 数据集管理模块
├── api_client.py             # Flask API客户端
├── quick_test.py             # 快速验证测试
├── qwen3_4b_evaluation.py    # 完整智商评测
├── performance_monitor.py    # 性能监控模块
├── run_evaluation.py         # 主运行脚本
└── flask_server.py           # RKLLM推理服务器
```

## 安装和配置

### 1. 环境要求

- Python 3.8+
- RK3588开发板
- 麒麟v10 sp1操作系统
- 至少8GB内存
- 15GB可用存储空间

### 2. 安装依赖

```bash
# 安装Python依赖包
pip install -r requirements.txt

# 检查依赖是否完整
python run_evaluation.py --check-deps
```

### 3. 启动RKLLM服务

首先启动flask_server.py服务：

```bash
# 启动RKLLM推理服务
python flask_server.py \
    --rkllm_model_path /path/to/your/qwen3-4b.rkllm \
    --target_platform rk3588
```

确保服务在 `http://localhost:8080` 正常运行。

## 使用指南

### 1. 下载评测数据集

```bash
# 下载所有数据集
python run_evaluation.py --mode download

# 下载指定数据集
python run_evaluation.py --mode download --datasets openbookqa mmlu gsm8k
```

### 2. 快速验证测试

测试3个核心数据集（OpenBookQA、MMLU、GSM8K），用于快速验证系统功能：

```bash
python run_evaluation.py --mode quick
```

### 3. 完整智商评测

执行全部10项智商测试：

```bash
# 仅智商评测
python run_evaluation.py --mode full

# 包含性能测试的完整评测
python run_evaluation.py --mode full --include-performance
```

### 4. 性能测试

单独进行性能监控测试：

```bash
python run_evaluation.py --mode performance
```

### 5. 自定义配置

```bash
# 指定服务器地址
python run_evaluation.py --mode quick --server-url http://*************:8080

# 指定数据集目录
python run_evaluation.py --mode full --data-dir /path/to/datasets
```

## 评测数据集详情

| 数据集 | 基准分数 | 测试样本 | 评测能力 | 题目类型 |
|--------|----------|----------|----------|----------|
| **OpenBookQA** | 0.382 | 5题 | 科学知识和常识问答 | 选择题 |
| **TriviaQA** | 0.508 | 5题 | 广泛知识储备 | 开放式问答 |
| **HellaSwag** | 0.555 | 3题 | 常识推理 | 情境续写 |
| **SQuAD2** | 0.588 | 4题 | 阅读理解 | 基于文本问答 |
| **XWINO** | 0.891 | 3题 | 代词指代推理 | 指代消解 |
| **MMLU** | 0.729 | 4题 | 多学科理解 | 学术选择题 |
| **GSM8K** | 0.719 | 4题 | 数学应用题 | 数学推理 |
| **MATH** | 0.520 | 3题 | 高中数学竞赛 | 复杂数学 |
| **BBH** | 0.594 | 2题 | 困难推理任务 | 逻辑推理 |
| **HumanEval** | 0.617 | 2题 | 代码生成 | 编程能力 |

## 性能指标标准

### 速度性能

| 指标 | 优秀标准 | 目标值 | 最低标准 |
|------|----------|--------|----------|
| 首Token延迟 | < 300ms | 300-400ms | < 500ms |
| Token生成速度 | > 25 tokens/s | 18-25 tokens/s | > 15 tokens/s |
| 批处理吞吐量 | > 80 tokens/s | 60-80 tokens/s | > 50 tokens/s |

### 资源利用

| 指标 | 优秀标准 | 目标值 | 最低标准 |
|------|----------|--------|----------|
| 模型内存占用 | < 1.5GB | 1.5-1.8GB | < 2GB |
| 系统可用内存 | > 7GB | > 6.5GB | > 6GB |
| CPU利用率 | < 15% | < 20% | < 30% |

### 智商能力保持

| 等级 | 平均保持率 | 单项最低 | 评价 |
|------|------------|----------|------|
| **优秀** | > 99% | > 97% | 智商能力基本无损失 |
| **良好** | > 98% | > 95% | 智商能力轻微下降 |
| **合格** | > 95% | > 92% | 智商能力可接受下降 |
| **不合格** | < 95% | < 92% | 智商能力下降过多 |

## 输出文件说明

### 结果文件 (results/)

- `quick_test_YYYYMMDD_HHMMSS.json`: 快速测试结果
- `comprehensive_evaluation_YYYYMMDD_HHMMSS.json`: 完整评测结果

### 报告文件 (reports/)

- `evaluation_report_YYYYMMDD_HHMMSS.md`: 智商评测报告
- `performance_report_YYYYMMDD_HHMMSS.md`: 性能测试报告

### 日志文件

- `evaluation_YYYYMMDD_HHMMSS.log`: 运行日志

## 故障排除

### 常见问题

1. **服务器连接失败**
   ```
   ✗ 服务器连接失败，请确保flask_server.py正在运行
   ```
   - 检查flask_server.py是否正常启动
   - 确认服务器地址和端口正确
   - 检查防火墙设置

2. **数据集下载失败**
   ```
   ✗ openbookqa 下载失败: Connection timeout
   ```
   - 检查网络连接
   - 使用代理或镜像源
   - 手动下载数据集

3. **内存不足**
   ```
   RuntimeError: CUDA out of memory
   ```
   - 减少批处理大小
   - 关闭其他程序释放内存
   - 检查系统内存使用情况

4. **推理超时**
   ```
   请求超时
   ```
   - 增加超时时间设置
   - 检查模型加载状态
   - 优化推理参数

### 调试模式

启用详细日志输出：

```bash
export PYTHONPATH=.
python -u run_evaluation.py --mode quick 2>&1 | tee debug.log
```

## 开发和扩展

### 添加新数据集

1. 在 `evaluation_datasets.py` 中添加数据集配置
2. 实现对应的样本提取方法
3. 在评测器中添加评估逻辑

### 自定义评测指标

1. 修改 `performance_monitor.py` 中的性能标准
2. 添加新的监控指标
3. 更新报告生成模板

### API扩展

1. 在 `api_client.py` 中添加新的API接口
2. 支持更多的推理参数
3. 实现流式响应处理

## 许可证

本项目遵循MIT许可证。

## 联系方式

如有问题或建议，请联系开发团队。

---

*文档版本: v1.0*  
*最后更新: 2025-08-02*
