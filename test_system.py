#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RK3588 Qwen3-4B 评测系统测试脚本
用于验证系统各个模块的功能
"""

import os
import sys
import time
import logging
from typing import Dict, Any

# 导入配置
from config import get_full_config, init_config

# 导入评测模块
from api_client import RKLLMAPIClient
from evaluation_datasets import EvaluationDatasets
from performance_monitor import PerformanceMonitor

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SystemTester:
    """系统测试器"""
    
    def __init__(self):
        """初始化测试器"""
        self.config = get_full_config()
        self.test_results = {}
        
    def test_config(self) -> bool:
        """测试配置模块"""
        logger.info("=== 测试配置模块 ===")
        
        try:
            # 初始化配置
            if not init_config():
                logger.error("配置初始化失败")
                return False
            
            # 检查配置完整性
            required_keys = ["server", "datasets", "performance_standards", "evaluation_modes"]
            for key in required_keys:
                if key not in self.config:
                    logger.error(f"配置缺少必要的键: {key}")
                    return False
            
            logger.info("✓ 配置模块测试通过")
            return True
            
        except Exception as e:
            logger.error(f"✗ 配置模块测试失败: {e}")
            return False
    
    def test_api_client(self) -> bool:
        """测试API客户端"""
        logger.info("=== 测试API客户端 ===")
        
        try:
            # 创建API客户端
            server_url = self.config["environment"]["server_url"]
            client = RKLLMAPIClient(base_url=server_url)
            
            # 测试健康检查
            logger.info("测试服务器连接...")
            if not client.health_check():
                logger.warning("⚠ 服务器连接失败，请确保flask_server.py正在运行")
                return False
            
            # 测试简单推理
            logger.info("测试简单推理...")
            result = client.simple_chat("你好，请说'测试成功'")
            
            if result.success:
                logger.info(f"✓ 推理成功: {result.response[:50]}...")
                logger.info(f"  延迟: {result.latency:.2f}ms")
                logger.info(f"  速度: {result.tokens_per_second:.1f} tokens/s")
            else:
                logger.error(f"✗ 推理失败: {result.error_message}")
                return False
            
            client.close()
            logger.info("✓ API客户端测试通过")
            return True
            
        except Exception as e:
            logger.error(f"✗ API客户端测试失败: {e}")
            return False
    
    def test_datasets_manager(self) -> bool:
        """测试数据集管理器"""
        logger.info("=== 测试数据集管理器 ===")
        
        try:
            # 创建数据集管理器
            data_dir = self.config["environment"]["data_dir"]
            manager = EvaluationDatasets(data_dir)
            
            # 测试配置加载
            logger.info("测试数据集配置...")
            config = manager.datasets_config
            if not config:
                logger.error("数据集配置为空")
                return False
            
            logger.info(f"✓ 加载了 {len(config)} 个数据集配置")
            
            # 测试基准分数获取
            logger.info("测试基准分数获取...")
            baseline_scores = manager.get_baseline_scores()
            if not baseline_scores:
                logger.error("基准分数为空")
                return False
            
            logger.info(f"✓ 获取了 {len(baseline_scores)} 个基准分数")
            
            # 测试数据集信息
            logger.info("测试数据集信息...")
            dataset_info = manager.get_dataset_info()
            if not dataset_info:
                logger.error("数据集信息为空")
                return False
            
            logger.info(f"✓ 获取了 {len(dataset_info)} 个数据集信息")
            
            # 尝试加载一个小数据集（如果网络可用）
            logger.info("尝试加载测试数据集...")
            try:
                # 尝试加载OpenBookQA数据集
                dataset = manager.load_dataset("openbookqa")
                if dataset:
                    logger.info("✓ 成功加载测试数据集")
                    
                    # 测试样本提取
                    samples = manager.get_test_samples("openbookqa")
                    if samples:
                        logger.info(f"✓ 提取了 {len(samples)} 个测试样本")
                    else:
                        logger.warning("⚠ 未能提取测试样本")
                else:
                    logger.warning("⚠ 数据集加载失败（可能是网络问题）")
            except Exception as e:
                logger.warning(f"⚠ 数据集加载测试跳过: {e}")
            
            logger.info("✓ 数据集管理器测试通过")
            return True
            
        except Exception as e:
            logger.error(f"✗ 数据集管理器测试失败: {e}")
            return False
    
    def test_performance_monitor(self) -> bool:
        """测试性能监控器"""
        logger.info("=== 测试性能监控器 ===")
        
        try:
            # 创建API客户端和性能监控器
            server_url = self.config["environment"]["server_url"]
            client = RKLLMAPIClient(base_url=server_url)
            monitor = PerformanceMonitor(client)
            
            # 测试系统指标获取
            logger.info("测试系统指标获取...")
            system_metrics = monitor.get_system_metrics()
            if system_metrics:
                logger.info("✓ 系统指标获取成功")
                logger.info(f"  内存占用: {system_metrics.get('model_memory_usage', 0):.2f}GB")
                logger.info(f"  CPU利用率: {system_metrics.get('cpu_utilization', 0):.1f}%")
            else:
                logger.warning("⚠ 系统指标获取失败")
            
            # 测试性能等级评估
            logger.info("测试性能等级评估...")
            from performance_monitor import PerformanceMetrics
            test_metrics = PerformanceMetrics(
                first_token_latency=350.0,
                token_generation_speed=20.0,
                model_memory_usage=1.6
            )
            
            levels = monitor.evaluate_performance_level(test_metrics)
            if levels:
                logger.info("✓ 性能等级评估成功")
                for metric, level in levels.items():
                    logger.info(f"  {metric}: {level}")
            else:
                logger.warning("⚠ 性能等级评估失败")
            
            # 测试报告生成
            logger.info("测试报告生成...")
            report = monitor.generate_performance_report(test_metrics)
            if report and len(report) > 100:
                logger.info("✓ 性能报告生成成功")
                logger.info(f"  报告长度: {len(report)} 字符")
            else:
                logger.warning("⚠ 性能报告生成失败")
            
            client.close()
            logger.info("✓ 性能监控器测试通过")
            return True
            
        except Exception as e:
            logger.error(f"✗ 性能监控器测试失败: {e}")
            return False
    
    def test_file_operations(self) -> bool:
        """测试文件操作"""
        logger.info("=== 测试文件操作 ===")
        
        try:
            # 测试目录创建
            test_dirs = ["test_results", "test_reports"]
            for dir_name in test_dirs:
                os.makedirs(dir_name, exist_ok=True)
                if os.path.exists(dir_name):
                    logger.info(f"✓ 目录创建成功: {dir_name}")
                else:
                    logger.error(f"✗ 目录创建失败: {dir_name}")
                    return False
            
            # 测试文件写入
            test_file = "test_results/test_output.txt"
            test_content = "这是一个测试文件\n测试时间: " + time.strftime("%Y-%m-%d %H:%M:%S")
            
            with open(test_file, 'w', encoding='utf-8') as f:
                f.write(test_content)
            
            if os.path.exists(test_file):
                logger.info("✓ 文件写入成功")
            else:
                logger.error("✗ 文件写入失败")
                return False
            
            # 测试文件读取
            with open(test_file, 'r', encoding='utf-8') as f:
                read_content = f.read()
            
            if read_content == test_content:
                logger.info("✓ 文件读取成功")
            else:
                logger.error("✗ 文件读取失败")
                return False
            
            # 清理测试文件
            for dir_name in test_dirs:
                if os.path.exists(dir_name):
                    import shutil
                    shutil.rmtree(dir_name)
            
            logger.info("✓ 文件操作测试通过")
            return True
            
        except Exception as e:
            logger.error(f"✗ 文件操作测试失败: {e}")
            return False
    
    def run_all_tests(self) -> Dict[str, bool]:
        """运行所有测试"""
        logger.info("=== 开始系统测试 ===")
        
        tests = [
            ("配置模块", self.test_config),
            ("文件操作", self.test_file_operations),
            ("数据集管理器", self.test_datasets_manager),
            ("API客户端", self.test_api_client),
            ("性能监控器", self.test_performance_monitor)
        ]
        
        results = {}
        
        for test_name, test_func in tests:
            logger.info(f"\n--- 测试 {test_name} ---")
            try:
                results[test_name] = test_func()
            except Exception as e:
                logger.error(f"测试 {test_name} 时发生异常: {e}")
                results[test_name] = False
        
        # 输出测试结果摘要
        logger.info("\n=== 测试结果摘要 ===")
        passed = 0
        total = len(results)
        
        for test_name, result in results.items():
            status = "✓ 通过" if result else "✗ 失败"
            logger.info(f"{test_name}: {status}")
            if result:
                passed += 1
        
        logger.info(f"\n总计: {passed}/{total} 项测试通过")
        
        if passed == total:
            logger.info("🎉 所有测试通过！系统准备就绪。")
        else:
            logger.warning("⚠ 部分测试失败，请检查相关模块。")
        
        return results

def main():
    """主函数"""
    print("RK3588 Qwen3-4B 评测系统测试")
    print("=" * 50)
    
    tester = SystemTester()
    results = tester.run_all_tests()
    
    # 根据测试结果设置退出码
    if all(results.values()):
        sys.exit(0)  # 所有测试通过
    else:
        sys.exit(1)  # 有测试失败

if __name__ == "__main__":
    main()
