#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速验证测试脚本
用于快速验证3个核心数据集(OpenBookQA, MMLU, GSM8K)的评测功能
"""

import os
import sys
import time
import json
import argparse
from typing import Dict, List, Any, Tuple
import logging

# 导入自定义模块
from evaluation_datasets import EvaluationDatasets
from api_client import R<PERSON><PERSON><PERSON>PIClient, InferenceResult

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class QuickEvaluator:
    """快速评测器"""
    
    def __init__(self, api_client: RKLLMAPIClient, datasets_manager: EvaluationDatasets):
        """
        初始化快速评测器
        
        Args:
            api_client: API客户端
            datasets_manager: 数据集管理器
        """
        self.api_client = api_client
        self.datasets_manager = datasets_manager
        
        # 核心数据集配置
        self.core_datasets = ["openbookqa", "mmlu", "gsm8k"]
        
        # 评测提示模板
        self.prompts = {
            "openbookqa": "请回答以下科学常识选择题。只需要回答选项字母（A、B、C或D）。\n\n问题：{question}\n选项：\n{choices}\n\n答案：",
            "mmlu": "请回答以下{subject}领域的选择题。只需要回答选项字母（A、B、C或D）。\n\n问题：{question}\n选项：\n{choices}\n\n答案：",
            "gsm8k": "请解答以下数学应用题，并给出最终的数值答案。\n\n问题：{question}\n\n请逐步解答："
        }
    
    def format_choices(self, choices: List[str]) -> str:
        """格式化选择题选项"""
        choice_letters = ['A', 'B', 'C', 'D']
        formatted = []
        for i, choice in enumerate(choices):
            if i < len(choice_letters):
                formatted.append(f"{choice_letters[i]}. {choice}")
        return "\n".join(formatted)
    
    def evaluate_openbookqa(self, samples: List[Dict[str, Any]]) -> Tuple[float, List[Dict]]:
        """评测OpenBookQA"""
        logger.info("开始评测 OpenBookQA...")
        
        correct = 0
        total = len(samples)
        results = []
        
        for i, sample in enumerate(samples):
            logger.info(f"处理 OpenBookQA 问题 {i+1}/{total}")
            
            # 构建提示
            choices_text = self.format_choices(sample["choices"])
            prompt = self.prompts["openbookqa"].format(
                question=sample["question"],
                choices=choices_text
            )
            
            # 调用模型
            result = self.api_client.simple_chat(prompt)
            
            if result.success:
                # 提取答案
                predicted_answer = self.extract_choice_answer(result.response)
                correct_answer = sample["answer"]
                
                is_correct = predicted_answer == correct_answer
                if is_correct:
                    correct += 1
                
                results.append({
                    "question": sample["question"],
                    "correct_answer": correct_answer,
                    "predicted_answer": predicted_answer,
                    "is_correct": is_correct,
                    "response": result.response,
                    "latency": result.latency
                })
                
                logger.info(f"预测: {predicted_answer}, 正确: {correct_answer}, {'✓' if is_correct else '✗'}")
            else:
                logger.error(f"推理失败: {result.error_message}")
                results.append({
                    "question": sample["question"],
                    "correct_answer": sample["answer"],
                    "predicted_answer": "ERROR",
                    "is_correct": False,
                    "response": result.error_message,
                    "latency": result.latency
                })
        
        accuracy = correct / total if total > 0 else 0
        logger.info(f"OpenBookQA 准确率: {accuracy:.3f} ({correct}/{total})")
        
        return accuracy, results
    
    def evaluate_mmlu(self, samples: List[Dict[str, Any]]) -> Tuple[float, List[Dict]]:
        """评测MMLU"""
        logger.info("开始评测 MMLU...")
        
        correct = 0
        total = len(samples)
        results = []
        
        for i, sample in enumerate(samples):
            logger.info(f"处理 MMLU 问题 {i+1}/{total}")
            
            # 构建提示
            choices_text = self.format_choices(sample["choices"])
            prompt = self.prompts["mmlu"].format(
                subject=sample.get("subject", "通用"),
                question=sample["question"],
                choices=choices_text
            )
            
            # 调用模型
            result = self.api_client.simple_chat(prompt)
            
            if result.success:
                # 提取答案
                predicted_answer = self.extract_choice_answer(result.response)
                correct_answer = sample["answer"]
                
                is_correct = predicted_answer == correct_answer
                if is_correct:
                    correct += 1
                
                results.append({
                    "question": sample["question"],
                    "subject": sample.get("subject", "unknown"),
                    "correct_answer": correct_answer,
                    "predicted_answer": predicted_answer,
                    "is_correct": is_correct,
                    "response": result.response,
                    "latency": result.latency
                })
                
                logger.info(f"预测: {predicted_answer}, 正确: {correct_answer}, {'✓' if is_correct else '✗'}")
            else:
                logger.error(f"推理失败: {result.error_message}")
                results.append({
                    "question": sample["question"],
                    "subject": sample.get("subject", "unknown"),
                    "correct_answer": sample["answer"],
                    "predicted_answer": "ERROR",
                    "is_correct": False,
                    "response": result.error_message,
                    "latency": result.latency
                })
        
        accuracy = correct / total if total > 0 else 0
        logger.info(f"MMLU 准确率: {accuracy:.3f} ({correct}/{total})")
        
        return accuracy, results
    
    def evaluate_gsm8k(self, samples: List[Dict[str, Any]]) -> Tuple[float, List[Dict]]:
        """评测GSM8K"""
        logger.info("开始评测 GSM8K...")
        
        correct = 0
        total = len(samples)
        results = []
        
        for i, sample in enumerate(samples):
            logger.info(f"处理 GSM8K 问题 {i+1}/{total}")
            
            # 构建提示
            prompt = self.prompts["gsm8k"].format(question=sample["question"])
            
            # 调用模型
            result = self.api_client.simple_chat(prompt)
            
            if result.success:
                # 提取数值答案
                predicted_answer = self.extract_numeric_answer(result.response)
                correct_answer = self.extract_numeric_answer(sample["answer"])
                
                is_correct = abs(predicted_answer - correct_answer) < 0.01 if predicted_answer is not None and correct_answer is not None else False
                if is_correct:
                    correct += 1
                
                results.append({
                    "question": sample["question"],
                    "correct_answer": correct_answer,
                    "predicted_answer": predicted_answer,
                    "is_correct": is_correct,
                    "response": result.response,
                    "latency": result.latency
                })
                
                logger.info(f"预测: {predicted_answer}, 正确: {correct_answer}, {'✓' if is_correct else '✗'}")
            else:
                logger.error(f"推理失败: {result.error_message}")
                results.append({
                    "question": sample["question"],
                    "correct_answer": self.extract_numeric_answer(sample["answer"]),
                    "predicted_answer": None,
                    "is_correct": False,
                    "response": result.error_message,
                    "latency": result.latency
                })
        
        accuracy = correct / total if total > 0 else 0
        logger.info(f"GSM8K 准确率: {accuracy:.3f} ({correct}/{total})")
        
        return accuracy, results
    
    def extract_choice_answer(self, response: str) -> str:
        """从响应中提取选择题答案"""
        response = response.strip().upper()
        
        # 查找A、B、C、D
        for choice in ['A', 'B', 'C', 'D']:
            if choice in response:
                return choice
        
        # 如果没有找到，返回第一个字符（如果是有效选项）
        if len(response) > 0 and response[0] in ['A', 'B', 'C', 'D']:
            return response[0]
        
        return "UNKNOWN"
    
    def extract_numeric_answer(self, text: str) -> float:
        """从文本中提取数值答案"""
        import re
        
        # 查找数字模式
        patterns = [
            r'答案是\s*([+-]?\d+\.?\d*)',
            r'答案：\s*([+-]?\d+\.?\d*)',
            r'=\s*([+-]?\d+\.?\d*)',
            r'([+-]?\d+\.?\d*)\s*$',  # 行末的数字
            r'([+-]?\d+\.?\d*)'  # 任何数字
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, text)
            if matches:
                try:
                    return float(matches[-1])  # 取最后一个匹配的数字
                except ValueError:
                    continue
        
        return None
    
    def run_quick_test(self) -> Dict[str, Any]:
        """运行快速测试"""
        logger.info("=== 开始快速验证测试 ===")
        
        # 检查服务器连接
        if not self.api_client.health_check():
            logger.error("无法连接到RKLLM服务器，请确保flask_server.py正在运行")
            return {"error": "服务器连接失败"}
        
        results = {}
        baseline_scores = self.datasets_manager.get_baseline_scores()
        
        for dataset_name in self.core_datasets:
            logger.info(f"\n=== 评测 {dataset_name.upper()} ===")
            
            # 获取测试样本
            samples = self.datasets_manager.get_test_samples(dataset_name)
            if not samples:
                logger.error(f"无法获取 {dataset_name} 的测试样本")
                continue
            
            # 执行评测
            if dataset_name == "openbookqa":
                accuracy, details = self.evaluate_openbookqa(samples)
            elif dataset_name == "mmlu":
                accuracy, details = self.evaluate_mmlu(samples)
            elif dataset_name == "gsm8k":
                accuracy, details = self.evaluate_gsm8k(samples)
            else:
                continue
            
            # 计算保持率
            baseline = baseline_scores[dataset_name]
            retention_rate = accuracy / baseline if baseline > 0 else 0
            
            results[dataset_name] = {
                "accuracy": accuracy,
                "baseline_score": baseline,
                "retention_rate": retention_rate,
                "samples_tested": len(samples),
                "details": details
            }
            
            logger.info(f"{dataset_name} 结果: 准确率={accuracy:.3f}, 基准={baseline:.3f}, 保持率={retention_rate:.1%}")
        
        # 计算总体统计
        if results:
            avg_retention = sum(r["retention_rate"] for r in results.values()) / len(results)
            results["summary"] = {
                "average_retention_rate": avg_retention,
                "datasets_tested": len(results),
                "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
            }
            
            logger.info(f"\n=== 快速测试完成 ===")
            logger.info(f"平均保持率: {avg_retention:.1%}")
        
        return results

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="快速验证测试脚本")
    parser.add_argument("--server-url", default="http://localhost:8080", help="RKLLM服务器地址")
    parser.add_argument("--output", default="quick_test_results.json", help="结果输出文件")
    parser.add_argument("--data-dir", default="evaluation_datasets", help="数据集目录")
    
    args = parser.parse_args()
    
    # 初始化组件
    api_client = RKLLMAPIClient(base_url=args.server_url)
    datasets_manager = EvaluationDatasets(data_dir=args.data_dir)
    evaluator = QuickEvaluator(api_client, datasets_manager)
    
    try:
        # 运行快速测试
        results = evaluator.run_quick_test()
        
        # 保存结果
        with open(args.output, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        
        logger.info(f"测试结果已保存到: {args.output}")
        
    except KeyboardInterrupt:
        logger.info("测试被用户中断")
    except Exception as e:
        logger.error(f"测试过程中发生错误: {e}")
    finally:
        api_client.close()

if __name__ == "__main__":
    main()
