#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
大语言模型评测数据集下载和管理模块
支持10个智商测试数据集的下载、加载和管理
"""

import os
import json
import random
from typing import Dict, List, Any, Optional, Tuple
from datasets import load_dataset, Dataset
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class EvaluationDatasets:
    """评测数据集管理类"""
    
    def __init__(self, data_dir: str = "evaluation_datasets"):
        """
        初始化数据集管理器
        
        Args:
            data_dir: 数据集存储目录
        """
        self.data_dir = data_dir
        os.makedirs(data_dir, exist_ok=True)
        
        # 数据集配置信息
        self.datasets_config = {
            "openbookqa": {
                "hf_name": "allenai/openbookqa",
                "description": "科学知识和常识问答数据集",
                "baseline_score": 0.382,
                "test_samples": 5,
                "capability": "基础科学知识理解"
            },
            "triviaqa": {
                "hf_name": "mandarjoshi/trivia_qa",
                "description": "大规模远程监督阅读理解挑战数据集",
                "baseline_score": 0.508,
                "test_samples": 5,
                "capability": "广泛知识储备"
            },
            "hellaswag": {
                "hf_name": "Rowan/hellaswag",
                "description": "常识自然语言推理数据集",
                "baseline_score": 0.555,
                "test_samples": 3,
                "capability": "日常情境理解"
            },
            "squad_v2": {
                "hf_name": "rajpurkar/squad_v2",
                "description": "Stanford问答数据集2.0版本",
                "baseline_score": 0.588,
                "test_samples": 4,
                "capability": "文本理解和信息提取"
            },
            "mmlu": {
                "hf_name": "cais/mmlu",
                "description": "大规模多任务语言理解数据集",
                "baseline_score": 0.729,
                "test_samples": 4,
                "capability": "57个学科综合知识"
            },
            "gsm8k": {
                "hf_name": "openai/gsm8k",
                "description": "8.5K高质量小学数学应用题",
                "baseline_score": 0.719,
                "test_samples": 4,
                "capability": "基础数学应用"
            },
            "math": {
                "hf_name": "hendrycks/competition_math",
                "description": "高中数学竞赛题数据集",
                "baseline_score": 0.520,
                "test_samples": 3,
                "capability": "高中数学竞赛"
            },
            "bbh": {
                "hf_name": "lukaemon/bbh",
                "description": "困难任务集合，测试复杂推理能力",
                "baseline_score": 0.594,
                "test_samples": 2,
                "capability": "复杂逻辑推理"
            },
            "humaneval": {
                "hf_name": "openai/openai_humaneval",
                "description": "164个编程问题，用于评估代码生成能力",
                "baseline_score": 0.617,
                "test_samples": 2,
                "capability": "编程能力"
            }
        }
        
        # XWINO需要特殊处理（从GitHub获取）
        self.xwino_config = {
            "description": "跨语言Winograd模式数据集，测试代词指代推理",
            "baseline_score": 0.891,
            "test_samples": 3,
            "capability": "代词指代理解",
            "github_url": "https://github.com/yandex-research/crosslingual_winograd"
        }
    
    def download_all_datasets(self) -> Dict[str, bool]:
        """
        批量下载所有数据集
        
        Returns:
            下载结果字典，键为数据集名称，值为是否成功
        """
        results = {}
        
        logger.info("开始批量下载评测数据集...")
        
        for name, config in self.datasets_config.items():
            logger.info(f"正在下载 {name}...")
            try:
                dataset = load_dataset(config["hf_name"])
                dataset_path = os.path.join(self.data_dir, name)
                dataset.save_to_disk(dataset_path)
                logger.info(f"✓ {name} 下载完成")
                results[name] = True
            except Exception as e:
                logger.error(f"✗ {name} 下载失败: {e}")
                results[name] = False
        
        # XWINO数据集需要特殊处理
        logger.info("XWINO数据集需要从GitHub手动下载")
        logger.info(f"GitHub地址: {self.xwino_config['github_url']}")
        results["xwino"] = False  # 标记为需要手动处理
        
        logger.info("数据集下载完成！")
        return results
    
    def load_dataset(self, dataset_name: str) -> Optional[Dataset]:
        """
        加载指定数据集
        
        Args:
            dataset_name: 数据集名称
            
        Returns:
            加载的数据集对象，失败返回None
        """
        if dataset_name == "xwino":
            logger.warning("XWINO数据集需要从GitHub手动下载")
            return None
            
        if dataset_name not in self.datasets_config:
            logger.error(f"未知数据集: {dataset_name}")
            return None
        
        dataset_path = os.path.join(self.data_dir, dataset_name)
        
        if not os.path.exists(dataset_path):
            logger.info(f"数据集 {dataset_name} 不存在，尝试下载...")
            try:
                config = self.datasets_config[dataset_name]
                dataset = load_dataset(config["hf_name"])
                dataset.save_to_disk(dataset_path)
                logger.info(f"✓ {dataset_name} 下载完成")
            except Exception as e:
                logger.error(f"✗ {dataset_name} 下载失败: {e}")
                return None
        
        try:
            from datasets import load_from_disk
            dataset = load_from_disk(dataset_path)
            logger.info(f"✓ {dataset_name} 加载成功")
            return dataset
        except Exception as e:
            logger.error(f"✗ {dataset_name} 加载失败: {e}")
            return None
    
    def get_test_samples(self, dataset_name: str, num_samples: Optional[int] = None) -> List[Dict[str, Any]]:
        """
        获取测试样本
        
        Args:
            dataset_name: 数据集名称
            num_samples: 样本数量，None则使用配置中的默认值
            
        Returns:
            测试样本列表
        """
        if dataset_name == "xwino":
            return self._get_xwino_samples(num_samples)
        
        dataset = self.load_dataset(dataset_name)
        if dataset is None:
            return []
        
        config = self.datasets_config[dataset_name]
        if num_samples is None:
            num_samples = config["test_samples"]
        
        # 根据不同数据集的结构提取样本
        if dataset_name == "openbookqa":
            return self._extract_openbookqa_samples(dataset, num_samples)
        elif dataset_name == "triviaqa":
            return self._extract_triviaqa_samples(dataset, num_samples)
        elif dataset_name == "hellaswag":
            return self._extract_hellaswag_samples(dataset, num_samples)
        elif dataset_name == "squad_v2":
            return self._extract_squad_v2_samples(dataset, num_samples)
        elif dataset_name == "mmlu":
            return self._extract_mmlu_samples(dataset, num_samples)
        elif dataset_name == "gsm8k":
            return self._extract_gsm8k_samples(dataset, num_samples)
        elif dataset_name == "math":
            return self._extract_math_samples(dataset, num_samples)
        elif dataset_name == "bbh":
            return self._extract_bbh_samples(dataset, num_samples)
        elif dataset_name == "humaneval":
            return self._extract_humaneval_samples(dataset, num_samples)
        else:
            logger.error(f"未实现数据集 {dataset_name} 的样本提取方法")
            return []
    
    def _extract_openbookqa_samples(self, dataset: Dataset, num_samples: int) -> List[Dict[str, Any]]:
        """提取OpenBookQA样本"""
        test_data = dataset["test"]
        samples = random.sample(list(test_data), min(num_samples, len(test_data)))
        
        result = []
        for sample in samples:
            result.append({
                "question": sample["question_stem"],
                "choices": [choice["text"] for choice in sample["choices"]["text"]],
                "answer": sample["answerKey"],
                "type": "multiple_choice"
            })
        return result
    
    def _extract_triviaqa_samples(self, dataset: Dataset, num_samples: int) -> List[Dict[str, Any]]:
        """提取TriviaQA样本"""
        test_data = dataset["test"]
        samples = random.sample(list(test_data), min(num_samples, len(test_data)))
        
        result = []
        for sample in samples:
            result.append({
                "question": sample["question"],
                "answer": sample["answer"]["value"] if "answer" in sample else "",
                "type": "open_qa"
            })
        return result
    
    def _extract_hellaswag_samples(self, dataset: Dataset, num_samples: int) -> List[Dict[str, Any]]:
        """提取HellaSwag样本"""
        test_data = dataset["validation"]  # HellaSwag使用validation作为测试集
        samples = random.sample(list(test_data), min(num_samples, len(test_data)))

        result = []
        for sample in samples:
            result.append({
                "context": sample["ctx"],
                "endings": sample["endings"],
                "answer": int(sample["label"]),
                "type": "sentence_completion"
            })
        return result

    def _extract_squad_v2_samples(self, dataset: Dataset, num_samples: int) -> List[Dict[str, Any]]:
        """提取SQuAD2样本"""
        test_data = dataset["validation"]
        samples = random.sample(list(test_data), min(num_samples, len(test_data)))

        result = []
        for sample in samples:
            result.append({
                "context": sample["context"],
                "question": sample["question"],
                "answers": sample["answers"]["text"] if sample["answers"]["text"] else [],
                "is_impossible": len(sample["answers"]["text"]) == 0,
                "type": "reading_comprehension"
            })
        return result

    def _extract_mmlu_samples(self, dataset: Dataset, num_samples: int) -> List[Dict[str, Any]]:
        """提取MMLU样本"""
        test_data = dataset["test"]
        samples = random.sample(list(test_data), min(num_samples, len(test_data)))

        result = []
        for sample in samples:
            choices = [sample["choices"][i] for i in range(len(sample["choices"]))]
            result.append({
                "question": sample["question"],
                "choices": choices,
                "answer": sample["answer"],
                "subject": sample["subject"],
                "type": "multiple_choice"
            })
        return result

    def _extract_gsm8k_samples(self, dataset: Dataset, num_samples: int) -> List[Dict[str, Any]]:
        """提取GSM8K样本"""
        test_data = dataset["test"]
        samples = random.sample(list(test_data), min(num_samples, len(test_data)))

        result = []
        for sample in samples:
            result.append({
                "question": sample["question"],
                "answer": sample["answer"],
                "type": "math_word_problem"
            })
        return result

    def _extract_math_samples(self, dataset: Dataset, num_samples: int) -> List[Dict[str, Any]]:
        """提取MATH样本"""
        test_data = dataset["test"]
        samples = random.sample(list(test_data), min(num_samples, len(test_data)))

        result = []
        for sample in samples:
            result.append({
                "problem": sample["problem"],
                "solution": sample["solution"],
                "level": sample["level"],
                "type": sample["type"],
                "answer_type": "math_competition"
            })
        return result

    def _extract_bbh_samples(self, dataset: Dataset, num_samples: int) -> List[Dict[str, Any]]:
        """提取BBH样本"""
        test_data = dataset["test"]
        samples = random.sample(list(test_data), min(num_samples, len(test_data)))

        result = []
        for sample in samples:
            result.append({
                "input": sample["input"],
                "target": sample["target"],
                "type": "complex_reasoning"
            })
        return result

    def _extract_humaneval_samples(self, dataset: Dataset, num_samples: int) -> List[Dict[str, Any]]:
        """提取HumanEval样本"""
        test_data = dataset["test"]
        samples = random.sample(list(test_data), min(num_samples, len(test_data)))

        result = []
        for sample in samples:
            result.append({
                "prompt": sample["prompt"],
                "canonical_solution": sample["canonical_solution"],
                "test": sample["test"],
                "entry_point": sample["entry_point"],
                "type": "code_generation"
            })
        return result

    def _get_xwino_samples(self, num_samples: Optional[int] = None) -> List[Dict[str, Any]]:
        """获取XWINO样本（示例数据）"""
        if num_samples is None:
            num_samples = 3

        # 提供一些示例XWINO样本
        xwino_examples = [
            {
                "sentence": "The trophy doesn't fit into the brown suitcase because it's too large.",
                "pronoun": "it",
                "option1": "trophy",
                "option2": "suitcase",
                "answer": "trophy",
                "type": "pronoun_resolution"
            },
            {
                "sentence": "The city councilmen refused the demonstrators a permit because they feared violence.",
                "pronoun": "they",
                "option1": "councilmen",
                "option2": "demonstrators",
                "answer": "councilmen",
                "type": "pronoun_resolution"
            },
            {
                "sentence": "The delivery truck zoomed by the school bus because it was going so fast.",
                "pronoun": "it",
                "option1": "truck",
                "option2": "bus",
                "answer": "truck",
                "type": "pronoun_resolution"
            }
        ]

        return xwino_examples[:num_samples]
    
    def get_dataset_info(self) -> Dict[str, Dict[str, Any]]:
        """
        获取所有数据集的信息
        
        Returns:
            数据集信息字典
        """
        info = dict(self.datasets_config)
        info["xwino"] = self.xwino_config
        return info
    
    def get_baseline_scores(self) -> Dict[str, float]:
        """
        获取所有数据集的基准分数
        
        Returns:
            基准分数字典
        """
        scores = {}
        for name, config in self.datasets_config.items():
            scores[name] = config["baseline_score"]
        scores["xwino"] = self.xwino_config["baseline_score"]
        return scores

if __name__ == "__main__":
    # 测试代码
    datasets_manager = EvaluationDatasets()
    
    # 显示数据集信息
    print("=== 数据集信息 ===")
    info = datasets_manager.get_dataset_info()
    for name, config in info.items():
        print(f"{name}: {config['description']} (基准分数: {config['baseline_score']})")
    
    # 下载数据集（可选）
    # results = datasets_manager.download_all_datasets()
    # print("\n=== 下载结果 ===")
    # for name, success in results.items():
    #     status = "成功" if success else "失败"
    #     print(f"{name}: {status}")
