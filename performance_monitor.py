#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
性能监控和报告生成模块
监控推理性能指标并生成Markdown格式报告
"""

import os
import sys
import time
import json
import psutil
import threading
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
import logging

# 导入自定义模块
from api_client import RKLLMAPIClient

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

@dataclass
class PerformanceMetrics:
    """性能指标数据类"""
    # 速度性能
    first_token_latency: float = 0.0  # 首Token延迟 (ms)
    token_generation_speed: float = 0.0  # Token生成速度 (tokens/s)
    batch_throughput: float = 0.0  # 批处理吞吐量 (tokens/s)
    model_load_time: float = 0.0  # 模型加载时间 (s)
    memory_allocation_time: float = 0.0  # 内存分配时间 (s)
    
    # 资源利用
    model_memory_usage: float = 0.0  # 模型内存占用 (GB)
    system_available_memory: float = 0.0  # 系统可用内存 (GB)
    cpu_utilization: float = 0.0  # CPU利用率 (%)
    memory_fragmentation: float = 0.0  # 内存碎片率 (%)
    
    # 功耗散热（模拟数据，实际需要硬件支持）
    total_power: float = 0.0  # 整机功耗 (W)
    npu_power: float = 0.0  # NPU功耗 (W)
    chip_temperature: float = 0.0  # 芯片温度 (°C)
    
    # 时间戳
    timestamp: str = ""

class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self, api_client: RKLLMAPIClient):
        """
        初始化性能监控器
        
        Args:
            api_client: API客户端
        """
        self.api_client = api_client
        self.monitoring = False
        self.metrics_history = []
        
        # 性能标准定义
        self.performance_standards = {
            "first_token_latency": {"excellent": 300, "good": 400, "minimum": 500},  # ms
            "token_generation_speed": {"excellent": 25, "good": 18, "minimum": 15},  # tokens/s
            "batch_throughput": {"excellent": 80, "good": 60, "minimum": 50},  # tokens/s
            "model_load_time": {"excellent": 5, "good": 8, "minimum": 10},  # s
            "memory_allocation_time": {"excellent": 1, "good": 1.5, "minimum": 2},  # s
            "model_memory_usage": {"excellent": 1.5, "good": 1.8, "minimum": 2.0},  # GB
            "system_available_memory": {"excellent": 7, "good": 6.5, "minimum": 6},  # GB
            "cpu_utilization": {"excellent": 15, "good": 20, "minimum": 30},  # %
            "memory_fragmentation": {"excellent": 3, "good": 5, "minimum": 10},  # %
            "total_power": {"excellent": 6, "good": 8, "minimum": 10},  # W
            "npu_power": {"excellent": 4, "good": 5, "minimum": 6},  # W
            "chip_temperature": {"excellent": 60, "good": 65, "minimum": 70}  # °C
        }
    
    def get_system_metrics(self) -> Dict[str, float]:
        """获取系统性能指标"""
        try:
            # 内存信息
            memory = psutil.virtual_memory()
            system_available_memory = memory.available / (1024**3)  # GB
            memory_fragmentation = (memory.used / memory.total) * 100  # %
            
            # CPU利用率
            cpu_utilization = psutil.cpu_percent(interval=1)
            
            # 模拟NPU和功耗数据（实际需要硬件API）
            total_power = 7.5  # 模拟值
            npu_power = 4.2  # 模拟值
            chip_temperature = 62.0  # 模拟值
            
            # 估算模型内存占用（简单估算）
            process = psutil.Process()
            model_memory_usage = process.memory_info().rss / (1024**3)  # GB
            
            return {
                "model_memory_usage": model_memory_usage,
                "system_available_memory": system_available_memory,
                "cpu_utilization": cpu_utilization,
                "memory_fragmentation": memory_fragmentation,
                "total_power": total_power,
                "npu_power": npu_power,
                "chip_temperature": chip_temperature
            }
        except Exception as e:
            logger.error(f"获取系统指标失败: {e}")
            return {}
    
    def measure_inference_performance(self, test_prompts: List[str]) -> PerformanceMetrics:
        """
        测量推理性能
        
        Args:
            test_prompts: 测试提示列表
            
        Returns:
            性能指标
        """
        logger.info("开始测量推理性能...")
        
        metrics = PerformanceMetrics()
        metrics.timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        # 获取系统指标
        system_metrics = self.get_system_metrics()
        for key, value in system_metrics.items():
            if hasattr(metrics, key):
                setattr(metrics, key, value)
        
        # 测量首Token延迟
        logger.info("测量首Token延迟...")
        first_token_latencies = []
        for prompt in test_prompts[:3]:  # 使用前3个提示测试
            start_time = time.time()
            result = self.api_client.simple_chat(prompt)
            if result.success:
                first_token_latency = (time.time() - start_time) * 1000  # 转换为ms
                first_token_latencies.append(first_token_latency)
        
        if first_token_latencies:
            metrics.first_token_latency = sum(first_token_latencies) / len(first_token_latencies)
        
        # 测量Token生成速度
        logger.info("测量Token生成速度...")
        token_speeds = []
        for prompt in test_prompts:
            result = self.api_client.simple_chat(prompt)
            if result.success and result.tokens_per_second > 0:
                token_speeds.append(result.tokens_per_second)
        
        if token_speeds:
            metrics.token_generation_speed = sum(token_speeds) / len(token_speeds)
        
        # 测量批处理吞吐量
        logger.info("测量批处理吞吐量...")
        batch_start_time = time.time()
        batch_results = self.api_client.batch_inference(test_prompts, delay_between_requests=0.05)
        batch_duration = time.time() - batch_start_time
        
        total_tokens = sum(len(r.response) / 4 for r in batch_results if r.success)  # 估算token数
        if batch_duration > 0:
            metrics.batch_throughput = total_tokens / batch_duration
        
        # 模拟模型加载时间和内存分配时间（实际需要在模型初始化时测量）
        metrics.model_load_time = 6.5  # 模拟值
        metrics.memory_allocation_time = 1.2  # 模拟值
        
        logger.info("性能测量完成")
        return metrics
    
    def evaluate_performance_level(self, metrics: PerformanceMetrics) -> Dict[str, str]:
        """
        评估性能等级
        
        Args:
            metrics: 性能指标
            
        Returns:
            各指标的性能等级
        """
        levels = {}
        
        for metric_name, standards in self.performance_standards.items():
            if hasattr(metrics, metric_name):
                value = getattr(metrics, metric_name)
                
                # 对于延迟、时间、功耗、温度等指标，越小越好
                if metric_name in ["first_token_latency", "model_load_time", "memory_allocation_time", 
                                 "model_memory_usage", "cpu_utilization", "memory_fragmentation",
                                 "total_power", "npu_power", "chip_temperature"]:
                    if value <= standards["excellent"]:
                        levels[metric_name] = "优秀"
                    elif value <= standards["good"]:
                        levels[metric_name] = "良好"
                    elif value <= standards["minimum"]:
                        levels[metric_name] = "合格"
                    else:
                        levels[metric_name] = "不合格"
                
                # 对于速度、内存等指标，越大越好
                else:
                    if value >= standards["excellent"]:
                        levels[metric_name] = "优秀"
                    elif value >= standards["good"]:
                        levels[metric_name] = "良好"
                    elif value >= standards["minimum"]:
                        levels[metric_name] = "合格"
                    else:
                        levels[metric_name] = "不合格"
        
        return levels
    
    def generate_performance_report(self, metrics: PerformanceMetrics, 
                                  evaluation_results: Optional[Dict] = None) -> str:
        """
        生成性能报告
        
        Args:
            metrics: 性能指标
            evaluation_results: 评测结果（可选）
            
        Returns:
            Markdown格式的报告
        """
        levels = self.evaluate_performance_level(metrics)
        
        report = f"""# RK3588 + Qwen3-4B 性能评测报告

## 测试信息
- **测试时间**: {metrics.timestamp}
- **测试平台**: RK3588
- **模型**: Qwen3-4B
- **测试环境**: 麒麟v10 sp1

## 性能指标详情

### 速度性能

| 指标名称 | 测试值 | 性能等级 | 最低标准 | 目标值 | 优秀标准 |
|---------|--------|----------|----------|--------|----------|
| 首Token延迟 | {metrics.first_token_latency:.1f}ms | {levels.get('first_token_latency', 'N/A')} | < 500ms | 300-400ms | < 300ms |
| Token生成速度 | {metrics.token_generation_speed:.1f} tokens/s | {levels.get('token_generation_speed', 'N/A')} | > 15 tokens/s | 18-25 tokens/s | > 25 tokens/s |
| 批处理吞吐量 | {metrics.batch_throughput:.1f} tokens/s | {levels.get('batch_throughput', 'N/A')} | > 50 tokens/s | 60-80 tokens/s | > 80 tokens/s |
| 模型加载时间 | {metrics.model_load_time:.1f}s | {levels.get('model_load_time', 'N/A')} | < 10秒 | 5-8秒 | < 5秒 |
| 内存分配时间 | {metrics.memory_allocation_time:.1f}s | {levels.get('memory_allocation_time', 'N/A')} | < 2秒 | 1-1.5秒 | < 1秒 |

### 资源利用

| 指标名称 | 测试值 | 性能等级 | 最低标准 | 目标值 | 优秀标准 |
|---------|--------|----------|----------|--------|----------|
| 模型内存占用 | {metrics.model_memory_usage:.2f}GB | {levels.get('model_memory_usage', 'N/A')} | < 2GB | 1.5-1.8GB | < 1.5GB |
| 系统可用内存 | {metrics.system_available_memory:.2f}GB | {levels.get('system_available_memory', 'N/A')} | > 6GB | > 6.5GB | > 7GB |
| CPU利用率 | {metrics.cpu_utilization:.1f}% | {levels.get('cpu_utilization', 'N/A')} | < 30% | < 20% | < 15% |
| 内存碎片率 | {metrics.memory_fragmentation:.1f}% | {levels.get('memory_fragmentation', 'N/A')} | < 10% | < 5% | < 3% |

### 功耗散热

| 指标名称 | 测试值 | 性能等级 | 最低标准 | 目标值 | 优秀标准 |
|---------|--------|----------|----------|--------|----------|
| 整机功耗 | {metrics.total_power:.1f}W | {levels.get('total_power', 'N/A')} | < 10W | < 8W | < 6W |
| NPU功耗 | {metrics.npu_power:.1f}W | {levels.get('npu_power', 'N/A')} | < 6W | < 5W | < 4W |
| 芯片温度 | {metrics.chip_temperature:.1f}°C | {levels.get('chip_temperature', 'N/A')} | < 70°C | < 65°C | < 60°C |

## 性能等级统计

"""
        
        # 统计各等级数量
        level_counts = {"优秀": 0, "良好": 0, "合格": 0, "不合格": 0}
        for level in levels.values():
            if level in level_counts:
                level_counts[level] += 1
        
        total_metrics = len(levels)
        for level, count in level_counts.items():
            percentage = (count / total_metrics * 100) if total_metrics > 0 else 0
            report += f"- **{level}**: {count}项 ({percentage:.1f}%)\n"
        
        # 添加智商评测结果（如果提供）
        if evaluation_results and "summary" in evaluation_results:
            summary = evaluation_results["summary"]
            report += f"""

## 智商能力保持情况

- **平均保持率**: {summary.get('average_retention_rate', 0):.1%}
- **测试数据集**: {summary.get('datasets_tested', 0)}个
- **测试时间**: {summary.get('timestamp', 'N/A')}

### 各数据集详情

| 数据集 | 准确率 | 基准分数 | 保持率 | 评测能力 |
|--------|--------|----------|--------|----------|"""
            
            for dataset_name, result in evaluation_results.items():
                if dataset_name != "summary" and isinstance(result, dict):
                    accuracy = result.get("accuracy", 0)
                    baseline = result.get("baseline_score", 0)
                    retention = result.get("retention_rate", 0)
                    
                    # 获取数据集描述
                    from evaluation_datasets import EvaluationDatasets
                    datasets_info = EvaluationDatasets().get_dataset_info()
                    capability = datasets_info.get(dataset_name, {}).get("capability", "未知")
                    
                    report += f"\n| {dataset_name.upper()} | {accuracy:.3f} | {baseline:.3f} | {retention:.1%} | {capability} |"
        
        report += f"""

## 总体评价

基于当前测试结果，RK3588平台上的Qwen3-4B模型表现为：

"""
        
        # 根据性能等级给出总体评价
        excellent_count = level_counts["优秀"]
        good_count = level_counts["良好"]
        acceptable_count = level_counts["合格"]
        poor_count = level_counts["不合格"]
        
        if excellent_count >= total_metrics * 0.7:
            report += "**优秀级别** - 各项性能指标表现出色，完全满足部署要求。"
        elif excellent_count + good_count >= total_metrics * 0.7:
            report += "**良好级别** - 大部分性能指标表现良好，基本满足部署要求。"
        elif poor_count <= total_metrics * 0.2:
            report += "**合格级别** - 性能指标基本达标，可以满足基本使用需求。"
        else:
            report += "**需要优化** - 部分关键性能指标未达标，建议进行优化后再部署。"
        
        report += f"""

## 建议和后续优化方向

1. **性能优化重点**:
   - 关注不合格指标的优化
   - 提升Token生成速度
   - 降低内存占用和功耗

2. **监控建议**:
   - 持续监控温度和功耗
   - 定期检查内存碎片情况
   - 监控长时间运行的稳定性

3. **下一步计划**:
   - 进行NPU优化部署
   - 实施量化和剪枝优化
   - 完善散热方案

---
*报告生成时间: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}*
*测试工具: RK3588 Qwen3-4B 评测系统*
"""
        
        return report

def main():
    """主函数 - 用于测试"""
    # 创建API客户端
    api_client = RKLLMAPIClient()
    
    # 创建性能监控器
    monitor = PerformanceMonitor(api_client)
    
    # 测试提示
    test_prompts = [
        "请介绍一下人工智能的发展历史。",
        "解释一下机器学习和深度学习的区别。",
        "什么是大语言模型？它有哪些应用？",
        "请计算 123 + 456 = ?",
        "写一个Python函数来计算斐波那契数列。"
    ]
    
    try:
        # 测量性能
        metrics = monitor.measure_inference_performance(test_prompts)
        
        # 生成报告
        report = monitor.generate_performance_report(metrics)
        
        # 保存报告
        with open("performance_report.md", "w", encoding="utf-8") as f:
            f.write(report)
        
        logger.info("性能报告已生成: performance_report.md")
        
    except Exception as e:
        logger.error(f"性能测试失败: {e}")
    finally:
        api_client.close()

if __name__ == "__main__":
    main()
