# RK3588 Qwen3-4B 评测系统使用指南

## 🎯 系统概述

基于优化计划第三步骤，已完成RK3588平台Qwen3-4B模型的完整智商评测系统。

## 📁 核心文件

| 文件名 | 功能描述 |
|--------|----------|
| `run_evaluation.py` | **主运行脚本** - 一键执行各种评测模式 |
| `evaluation_datasets.py` | 数据集管理 - 10个智商测试数据集 |
| `api_client.py` | Flask API客户端 - 与flask_server.py通信 |
| `qwen3_4b_evaluation.py` | 完整智商评测 - 10项测试全覆盖 |
| `performance_monitor.py` | 性能监控 - 延迟、速度、内存监控 |
| `quick_test.py` | 快速验证 - 3个核心数据集测试 |
| `test_system.py` | 系统测试 - 验证各模块功能 |
| `config.py` | 配置管理 - 集中配置参数 |

## 🚀 快速开始

### 1. 环境准备
```bash
# 安装依赖
pip install -r requirements.txt

# 验证系统
python test_system.py
```

### 2. 启动服务
```bash
# 启动Flask推理服务
python flask_server.py --rkllm_model_path /path/to/qwen3-4b.rkllm
```

### 3. 运行评测
```bash
# 下载数据集
python run_evaluation.py --mode download

# 快速验证（3个数据集）
python run_evaluation.py --mode quick

# 完整评测（10个数据集 + 性能监控）
python run_evaluation.py --mode full --include-performance
```

## 📊 评测内容

### 智商测试（10项）
| 数据集 | 基准分数 | 测试样本 | 评测能力 |
|--------|----------|----------|----------|
| OpenBookQA | 0.382 | 5题 | 科学常识 |
| TriviaQA | 0.508 | 5题 | 知识问答 |
| HellaSwag | 0.555 | 3题 | 常识推理 |
| SQuAD2 | 0.588 | 4题 | 阅读理解 |
| XWINO | 0.891 | 3题 | 指代消解 |
| MMLU | 0.729 | 4题 | 多学科理解 |
| GSM8K | 0.719 | 4题 | 数学应用 |
| MATH | 0.520 | 3题 | 竞赛数学 |
| BBH | 0.594 | 2题 | 困难推理 |
| HumanEval | 0.617 | 2题 | 代码生成 |

### 性能监控
- **速度**: 首Token延迟、生成速度、吞吐量
- **资源**: 内存占用、CPU利用率、系统可用内存
- **功耗**: 整机功耗、NPU功耗、芯片温度

## 📈 评测标准

### 智商能力保持率
- **优秀**: >99% (智商能力基本无损失)
- **良好**: >98% (智商能力轻微下降)
- **合格**: >95% (智商能力可接受下降)
- **不合格**: <95% (智商能力下降过多)

### 性能指标
- **首Token延迟**: 目标<500ms
- **Token生成速度**: 目标>15 tokens/s
- **模型内存占用**: 目标<2GB
- **CPU利用率**: 目标<30%

## 📄 输出文件

### 结果文件
- `results/quick_test_*.json` - 快速测试结果
- `results/comprehensive_evaluation_*.json` - 完整评测结果

### 报告文件
- `reports/evaluation_report_*.md` - 智商评测报告
- `reports/performance_report_*.md` - 性能测试报告

### 日志文件
- `evaluation_*.log` - 运行日志

## 🔧 常用命令

```bash
# 检查依赖
python run_evaluation.py --check-deps

# 下载指定数据集
python run_evaluation.py --mode download --datasets openbookqa mmlu gsm8k

# 自定义服务器地址
python run_evaluation.py --mode quick --server-url http://192.168.1.100:8080

# 仅性能测试
python run_evaluation.py --mode performance

# 系统功能验证
python test_system.py
```

## ⚠️ 注意事项

1. **确保Flask服务正常运行** - 评测前先启动flask_server.py
2. **网络连接** - 首次运行需要下载数据集，确保网络畅通
3. **存储空间** - 确保至少15GB可用空间
4. **内存要求** - 建议至少8GB内存

## 🎯 预期结果

运行完整评测后，应该得到：
- ✅ 智商保持率 > 95%（合格标准）
- ✅ 首Token延迟 < 500ms
- ✅ Token生成速度 > 15 tokens/s
- ✅ 详细的性能和智商评测报告

## 📞 故障排除

### 常见问题
1. **服务器连接失败** → 检查flask_server.py是否启动
2. **数据集下载失败** → 检查网络连接或使用代理
3. **内存不足** → 关闭其他程序释放内存
4. **推理超时** → 检查模型加载状态

### 调试模式
```bash
# 启用详细日志
python -u run_evaluation.py --mode quick 2>&1 | tee debug.log
```

---

**系统版本**: v1.0  
**适用平台**: RK3588 + 麒麟v10 sp1  
**模型**: Qwen3-4B  
**更新时间**: 2025-08-02
