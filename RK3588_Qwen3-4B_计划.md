# RK3588 + Qwen3-4B 优化实施计划

## 项目目标
在RK3588平台上部署Qwen3-4B模型，实现**高速度**和**高准确度**的AI推理服务。

### 核心性能指标

| 指标类别 | 指标名称 | 最低标准 | 目标值 | 优秀标准 | 状态 |
|---------|----------|----------|--------|----------|------|
| **速度性能** | 首Token延迟 | < 500ms | 300-400ms | < 300ms | 待测试 |
| | Token生成速度 | > 15 tokens/s | 18-25 tokens/s | > 25 tokens/s | 待测试 |
| | 批处理吞吐量 | > 50 tokens/s | 60-80 tokens/s | > 80 tokens/s | 待测试 |
| | 模型加载时间 | < 10秒 | 5-8秒 | < 5秒 | 待测试 |
| | 内存分配时间 | < 2秒 | 1-1.5秒 | < 1秒 | 待测试 |
| **准确度** | BLEU分数下降 | < 3% | < 2% | < 1% | 待测试 |
| | Rouge-L分数下降 | < 2% | < 1.5% | < 1% | 待测试 |
| | 语义相似度下降 | < 5% | < 3% | < 2% | 待测试 |
| | 事实准确性保持率 | > 95% | > 97% | > 98% | 待测试 |
| | 逻辑一致性保持率 | > 92% | > 95% | > 97% | 待测试 |
| | 多轮对话连贯性 | > 90% | > 93% | > 95% | 待测试 |
| **资源利用** | 模型内存占用 | < 2GB | 1.5-1.8GB | < 1.5GB | 待测试 |
| | 系统可用内存 | > 6GB | > 6.5GB | > 7GB | 待测试 |
| | NPU利用率 | > 85% | 90-95% | > 95% | 待测试 |
| | CPU利用率 | < 30% | < 20% | < 15% | 待测试 |
| | 内存碎片率 | < 10% | < 5% | < 3% | 待测试 |
| | 存储空间 | < 1GB | < 800MB | < 600MB | 待测试 |
| **功耗散热** | 整机功耗 | < 10W | < 8W | < 6W | 待测试 |
| | NPU功耗 | < 6W | < 5W | < 4W | 待测试 |
| | 芯片温度 | < 70°C | < 65°C | < 60°C | 待测试 |
| | 散热方案 | 被动散热 | 被动散热 | 被动散热 | 待测试 |

### 智商性能指标 (基准分数)

| 评测数据集 | 基准分数 | 最低保持 | 目标保持 | 优秀保持 | 评测能力 |
|-----------|----------|----------|----------|----------|----------|
| **OpenBookQA** | 0.382 | > 0.363 | > 0.374 | > 0.378 | 科学知识和常识问答 |
| **TriviaQA** | 0.508 | > 0.483 | > 0.498 | > 0.503 | 知识问答 |
| **HellaSwag** | 0.555 | > 0.527 | > 0.544 | > 0.550 | 常识推理 |
| **SQuAD2** | 0.588 | > 0.559 | > 0.576 | > 0.582 | 阅读理解 |
| **XWINO** | 0.891 | > 0.847 | > 0.873 | > 0.882 | 代词指代推理 |
| **MMLU** | 0.729 | > 0.693 | > 0.714 | > 0.722 | 多任务语言理解(53个学科) |
| **GSM8K** | 0.719 | > 0.683 | > 0.704 | > 0.712 | 数学应用题 |
| **MATH** | 0.520 | > 0.494 | > 0.510 | > 0.515 | 高中数学竞赛题 |
| **BBH** | 0.594 | > 0.565 | > 0.582 | > 0.588 | 困难任务集合 |
| **HumanEval** | 0.617 | > 0.586 | > 0.605 | > 0.611 | 代码生成 |

**说明**:
- 最低保持: 基准分数的95%以上
- 目标保持: 基准分数的98%以上
- 优秀保持: 基准分数的99%以上

### 综合智商评估标准

| 评估等级 | 平均保持率 | 单项最低 | 综合评价 |
|---------|------------|----------|----------|
| **优秀** | > 99% | > 97% | 智商能力基本无损失 |
| **良好** | > 98% | > 95% | 智商能力轻微下降 |
| **合格** | > 95% | > 92% | 智商能力可接受下降 |
| **不合格** | < 95% | < 92% | 智商能力下降过多 |


## 详细实施计划

### 阶段一：环境准备与基础评测 (第1-2周)

#### 1.1 硬件环境确认
- **RK3588开发板规格确认**
  - CPU: 不确定
  - NPU: 6 TOPS算力
  - 内存: 8GB
  - 存储: 确保至少15GB可用空间（原始模型较大）
  - 散热: 确认散热方案充足，支持长时间运行

- **软件环境搭建**
  - 麒麟v10 sp1
  - RKNN-Toolkit 最新版本
  - 必要的运行环境和依赖库

### 阶段二：启动Flask服务与智商考试评测 (第3-4周)

#### 2.1 Flask服务部署
- **启动flask_server.py服务**
  - 配置Flask Web服务器
  - 设置API接口端点
  - 配置模型推理服务
  - 建立HTTP请求处理机制
  - 设置跨域访问支持
  - 配置日志记录系统

- **服务功能验证**
  - 验证服务启动正常
  - 测试API接口响应
  - 检查模型加载状态
  - 验证推理功能正常
  - 测试并发请求处理
  - 监控服务稳定性

#### 2.2 智商考试系统部署
- **评测系统安装配置**
  ```bash
  # 1. 安装Python依赖包
  pip install -r requirements.txt

  # 2. 检查依赖完整性
  python run_evaluation.py --check-deps

  # 3. 系统功能验证
  python test_system.py
  ```

- **核心评测模块**
  - `evaluation_datasets.py` - 数据集管理模块
  - `api_client.py` - Flask API客户端
  - `quick_test.py` - 快速验证测试
  - `qwen3_4b_evaluation.py` - 完整智商评测
  - `performance_monitor.py` - 性能监控模块
  - `run_evaluation.py` - 主运行脚本

- **10项智商考试数据集准备**
  - **OpenBookQA**: 科学常识问答 (5题)
    - 基准分数: 0.382
    - 评测能力: 基础科学知识理解
    - 题目类型: 选择题，科学事实推理

  - **TriviaQA**: 知识问答 (5题)
    - 基准分数: 0.508
    - 评测能力: 广泛知识储备
    - 题目类型: 开放式问答，事实性知识

  - **HellaSwag**: 常识推理 (3题)
    - 基准分数: 0.555
    - 评测能力: 日常情境理解
    - 题目类型: 情境续写，常识判断

  - **SQuAD2**: 阅读理解 (4题)
    - 基准分数: 0.588
    - 评测能力: 文本理解和信息提取
    - 题目类型: 基于文本的问答，包含无答案题目

  - **XWINO**: 指代消解 (3题)
    - 基准分数: 0.891
    - 评测能力: 代词指代理解
    - 题目类型: 代词指代判断，语言理解

  - **MMLU**: 多学科理解 (4题)
    - 基准分数: 0.729
    - 评测能力: 57个学科综合知识
    - 题目类型: 学术选择题，跨学科知识

  - **GSM8K**: 小学数学 (4题)
    - 基准分数: 0.719
    - 评测能力: 基础数学应用
    - 题目类型: 数学应用题，逻辑推理

  - **MATH**: 竞赛数学 (3题)
    - 基准分数: 0.520
    - 评测能力: 高中数学竞赛
    - 题目类型: 高难度数学题，复杂计算

  - **BBH**: 困难推理 (2题)
    - 基准分数: 0.594
    - 评测能力: 复杂逻辑推理
    - 题目类型: 逻辑推理，抽象思维

  - **HumanEval**: 代码生成 (2题)
    - 基准分数: 0.617
    - 评测能力: 编程能力
    - 题目类型: Python函数编写，算法实现

#### 2.3 考试执行与评分
- **数据集下载准备**
  ```bash
  # 下载所有评测数据集
  python run_evaluation.py --mode download

  # 或下载指定数据集
  python run_evaluation.py --mode download --datasets openbookqa mmlu gsm8k
  ```

- **快速验证测试**
  ```bash
  # 运行快速验证（3个核心数据集）
  python run_evaluation.py --mode quick
  ```
  - 测试OpenBookQA、MMLU、GSM8K三个核心数据集
  - 验证评测系统与Flask服务正常通信
  - 生成初步性能和准确率报告

- **完整智商考试**
  ```bash
  # 运行完整评测（10个数据集）
  python run_evaluation.py --mode full

  # 包含性能监控的完整评测
  python run_evaluation.py --mode full --include-performance
  ```
  - 执行全部10项智商测试
  - 自动计算准确率和保持率
  - 生成详细的Markdown格式报告

- **性能专项测试**
  ```bash
  # 单独运行性能测试
  python run_evaluation.py --mode performance
  ```
  - 监控首Token延迟、生成速度、内存占用
  - 评估性能等级（优秀/良好/合格/不合格）
  - 生成性能优化建议

#### 2.4 服务集成测试与基准建立
- **系统集成验证**
  ```bash
  # 验证系统各模块功能
  python test_system.py
  ```
  - 验证Flask服务与评测系统通信正常
  - 测试API接口响应时间和准确性
  - 检查数据集加载和样本提取功能
  - 验证性能监控和报告生成

- **性能基准建立**
  - **速度性能基准**
    - 首Token延迟: 目标<500ms
    - Token生成速度: 目标>15 tokens/s
    - 批处理吞吐量: 目标>50 tokens/s

  - **智商能力基准**
    - 平均保持率: 目标>95%
    - 单项最低保持率: 目标>92%
    - 10个数据集全覆盖测试

  - **资源利用基准**
    - 模型内存占用: 目标<2GB
    - CPU利用率: 目标<30%
    - 系统可用内存: 目标>6GB

- **输出文件说明**
  - `results/` - JSON格式的详细评测数据
  - `reports/` - Markdown格式的可读报告
  - `logs/` - 运行日志和调试信息

#### 2.5 完整评测流程（一键执行）
```bash
# 第1步：启动Flask服务
python flask_server.py --rkllm_model_path /path/to/qwen3-4b.rkllm

# 第2步：安装依赖并验证系统
pip install -r requirements.txt
python test_system.py

# 第3步：下载数据集
python run_evaluation.py --mode download

# 第4步：快速验证
python run_evaluation.py --mode quick

# 第5步：完整评测
python run_evaluation.py --mode full --include-performance

# 第6步：查看报告
ls reports/evaluation_report_*.md
```

**预期输出**：
- 智商保持率 > 95%（合格）
- 首Token延迟 < 500ms
- Token生成速度 > 15 tokens/s
- 详细的性能和智商评测报告

---

## 评测系统详细使用说明

### 系统架构
```
RK3588 Qwen3-4B 评测系统
├── evaluation_datasets.py    # 数据集管理模块
├── api_client.py             # Flask API客户端
├── quick_test.py             # 快速验证测试
├── qwen3_4b_evaluation.py    # 完整智商评测
├── performance_monitor.py    # 性能监控模块
├── run_evaluation.py         # 主运行脚本
├── test_system.py            # 系统测试脚本
├── config.py                 # 配置管理
└── flask_server.py           # RKLLM推理服务器
```

### 环境要求
- Python 3.8+
- RK3588开发板
- 麒麟v10 sp1操作系统
- 至少8GB内存
- 15GB可用存储空间

### 核心功能
- **10项智商测试**: OpenBookQA、TriviaQA、HellaSwag、SQuAD2、XWINO、MMLU、GSM8K、MATH、BBH、HumanEval
- **性能监控**: 监控延迟、吞吐量、内存占用、功耗等关键指标
- **自动化评测**: 支持快速验证和完整评测两种模式
- **报告生成**: 自动生成Markdown格式的详细评测报告
- **Flask集成**: 与现有的flask_server.py完美配套

### 详细使用指南

#### 1. 安装和配置
```bash
# 安装Python依赖包
pip install -r requirements.txt

# 检查依赖是否完整
python run_evaluation.py --check-deps

# 系统功能验证
python test_system.py
```

#### 2. 启动RKLLM服务
```bash
# 启动RKLLM推理服务
python flask_server.py \
    --rkllm_model_path /path/to/your/qwen3-4b.rkllm \
    --target_platform rk3588
```
确保服务在 `http://localhost:8080` 正常运行。

#### 3. 运行评测

**下载评测数据集**
```bash
# 下载所有数据集
python run_evaluation.py --mode download

# 下载指定数据集
python run_evaluation.py --mode download --datasets openbookqa mmlu gsm8k
```

**快速验证测试**
```bash
python run_evaluation.py --mode quick
```
测试3个核心数据集（OpenBookQA、MMLU、GSM8K），用于快速验证系统功能。

**完整智商评测**
```bash
# 仅智商评测
python run_evaluation.py --mode full

# 包含性能测试的完整评测
python run_evaluation.py --mode full --include-performance
```
执行全部10项智商测试。

**性能测试**
```bash
python run_evaluation.py --mode performance
```
单独进行性能监控测试。

**自定义配置**
```bash
# 指定服务器地址
python run_evaluation.py --mode quick --server-url http://*************:8080

# 指定数据集目录
python run_evaluation.py --mode full --data-dir /path/to/datasets
```

### 性能指标标准

#### 速度性能
| 指标 | 优秀标准 | 目标值 | 最低标准 |
|------|----------|--------|----------|
| 首Token延迟 | < 300ms | 300-400ms | < 500ms |
| Token生成速度 | > 25 tokens/s | 18-25 tokens/s | > 15 tokens/s |
| 批处理吞吐量 | > 80 tokens/s | 60-80 tokens/s | > 50 tokens/s |

#### 资源利用
| 指标 | 优秀标准 | 目标值 | 最低标准 |
|------|----------|--------|----------|
| 模型内存占用 | < 1.5GB | 1.5-1.8GB | < 2GB |
| 系统可用内存 | > 7GB | > 6.5GB | > 6GB |
| CPU利用率 | < 15% | < 20% | < 30% |

#### 智商能力保持
| 等级 | 平均保持率 | 单项最低 | 评价 |
|------|------------|----------|------|
| **优秀** | > 99% | > 97% | 智商能力基本无损失 |
| **良好** | > 98% | > 95% | 智商能力轻微下降 |
| **合格** | > 95% | > 92% | 智商能力可接受下降 |
| **不合格** | < 95% | < 92% | 智商能力下降过多 |

### 输出文件说明

#### 结果文件 (results/)
- `quick_test_YYYYMMDD_HHMMSS.json`: 快速测试结果
- `comprehensive_evaluation_YYYYMMDD_HHMMSS.json`: 完整评测结果

#### 报告文件 (reports/)
- `evaluation_report_YYYYMMDD_HHMMSS.md`: 智商评测报告
- `performance_report_YYYYMMDD_HHMMSS.md`: 性能测试报告

#### 日志文件
- `evaluation_YYYYMMDD_HHMMSS.log`: 运行日志

### 故障排除

#### 常见问题
1. **服务器连接失败**
   - 检查flask_server.py是否正常启动
   - 确认服务器地址和端口正确
   - 检查防火墙设置

2. **数据集下载失败**
   - 检查网络连接
   - 使用代理或镜像源
   - 手动下载数据集

3. **内存不足**
   - 减少批处理大小
   - 关闭其他程序释放内存
   - 检查系统内存使用情况

4. **推理超时**
   - 增加超时时间设置
   - 检查模型加载状态
   - 优化推理参数

#### 调试模式
```bash
export PYTHONPATH=.
python -u run_evaluation.py --mode quick 2>&1 | tee debug.log
```

