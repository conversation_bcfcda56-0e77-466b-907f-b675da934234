# RK3588 + Qwen3-4B 优化实施计划

## 项目目标
在RK3588平台上部署Qwen3-4B模型，实现**高速度**和**高准确度**的AI推理服务。

### 核心性能指标

| 指标类别 | 指标名称 | 最低标准 | 目标值 | 优秀标准 | 状态 |
|---------|----------|----------|--------|----------|------|
| **速度性能** | 首Token延迟 | < 500ms | 300-400ms | < 300ms | 待测试 |
| | Token生成速度 | > 15 tokens/s | 18-25 tokens/s | > 25 tokens/s | 待测试 |
| | 批处理吞吐量 | > 50 tokens/s | 60-80 tokens/s | > 80 tokens/s | 待测试 |
| | 模型加载时间 | < 10秒 | 5-8秒 | < 5秒 | 待测试 |
| | 内存分配时间 | < 2秒 | 1-1.5秒 | < 1秒 | 待测试 |
| **准确度** | BLEU分数下降 | < 3% | < 2% | < 1% | 待测试 |
| | Rouge-L分数下降 | < 2% | < 1.5% | < 1% | 待测试 |
| | 语义相似度下降 | < 5% | < 3% | < 2% | 待测试 |
| | 事实准确性保持率 | > 95% | > 97% | > 98% | 待测试 |
| | 逻辑一致性保持率 | > 92% | > 95% | > 97% | 待测试 |
| | 多轮对话连贯性 | > 90% | > 93% | > 95% | 待测试 |
| **资源利用** | 模型内存占用 | < 2GB | 1.5-1.8GB | < 1.5GB | 待测试 |
| | 系统可用内存 | > 6GB | > 6.5GB | > 7GB | 待测试 |
| | NPU利用率 | > 85% | 90-95% | > 95% | 待测试 |
| | CPU利用率 | < 30% | < 20% | < 15% | 待测试 |
| | 内存碎片率 | < 10% | < 5% | < 3% | 待测试 |
| | 存储空间 | < 1GB | < 800MB | < 600MB | 待测试 |
| **功耗散热** | 整机功耗 | < 10W | < 8W | < 6W | 待测试 |
| | NPU功耗 | < 6W | < 5W | < 4W | 待测试 |
| | 芯片温度 | < 70°C | < 65°C | < 60°C | 待测试 |
| | 散热方案 | 被动散热 | 被动散热 | 被动散热 | 待测试 |

### 智商性能指标 (基准分数)

| 评测数据集 | 基准分数 | 最低保持 | 目标保持 | 优秀保持 | 评测能力 |
|-----------|----------|----------|----------|----------|----------|
| **OpenBookQA** | 0.382 | > 0.363 | > 0.374 | > 0.378 | 科学知识和常识问答 |
| **TriviaQA** | 0.508 | > 0.483 | > 0.498 | > 0.503 | 知识问答 |
| **HellaSwag** | 0.555 | > 0.527 | > 0.544 | > 0.550 | 常识推理 |
| **SQuAD2** | 0.588 | > 0.559 | > 0.576 | > 0.582 | 阅读理解 |
| **XWINO** | 0.891 | > 0.847 | > 0.873 | > 0.882 | 代词指代推理 |
| **MMLU** | 0.729 | > 0.693 | > 0.714 | > 0.722 | 多任务语言理解(53个学科) |
| **GSM8K** | 0.719 | > 0.683 | > 0.704 | > 0.712 | 数学应用题 |
| **MATH** | 0.520 | > 0.494 | > 0.510 | > 0.515 | 高中数学竞赛题 |
| **BBH** | 0.594 | > 0.565 | > 0.582 | > 0.588 | 困难任务集合 |
| **HumanEval** | 0.617 | > 0.586 | > 0.605 | > 0.611 | 代码生成 |

**说明**:
- 最低保持: 基准分数的95%以上
- 目标保持: 基准分数的98%以上
- 优秀保持: 基准分数的99%以上

### 综合智商评估标准

| 评估等级 | 平均保持率 | 单项最低 | 综合评价 |
|---------|------------|----------|----------|
| **优秀** | > 99% | > 97% | 智商能力基本无损失 |
| **良好** | > 98% | > 95% | 智商能力轻微下降 |
| **合格** | > 95% | > 92% | 智商能力可接受下降 |
| **不合格** | < 95% | < 92% | 智商能力下降过多 |


## 详细实施计划

### 阶段一：环境准备与基础评测 (第1-2周)

#### 1.1 硬件环境确认
- **RK3588开发板规格确认**
  - CPU: 不确定
  - NPU: 6 TOPS算力
  - 内存: 8GB
  - 存储: 确保至少15GB可用空间（原始模型较大）
  - 散热: 确认散热方案充足，支持长时间运行

- **软件环境搭建**
  - 麒麟v10 sp1
  - RKNN-Toolkit 最新版本
  - 必要的运行环境和依赖库

### 阶段二：启动Flask服务与智商考试评测 (第3-4周)

#### 2.1 Flask服务部署
- **启动flask_server.py服务**
  - 配置Flask Web服务器
  - 设置API接口端点
  - 配置模型推理服务
  - 建立HTTP请求处理机制
  - 设置跨域访问支持
  - 配置日志记录系统

- **服务功能验证**
  - 验证服务启动正常
  - 测试API接口响应
  - 检查模型加载状态
  - 验证推理功能正常
  - 测试并发请求处理
  - 监控服务稳定性

#### 2.2 智商考试系统部署
- **评测系统安装配置**
  - 安装基础依赖包
    - datasets transformers torch
    - openai requests (用于API调用)
    - 其他必要的Python库
  - 配置评测环境
  - 部署评测脚本文件

- **10项智商考试数据集准备**
  - **OpenBookQA**: 科学常识问答 (5题)
    - 基准分数: 0.382
    - 评测能力: 基础科学知识理解
    - 题目类型: 选择题，科学事实推理

  - **TriviaQA**: 知识问答 (5题)
    - 基准分数: 0.508
    - 评测能力: 广泛知识储备
    - 题目类型: 开放式问答，事实性知识

  - **HellaSwag**: 常识推理 (3题)
    - 基准分数: 0.555
    - 评测能力: 日常情境理解
    - 题目类型: 情境续写，常识判断

  - **SQuAD2**: 阅读理解 (4题)
    - 基准分数: 0.588
    - 评测能力: 文本理解和信息提取
    - 题目类型: 基于文本的问答，包含无答案题目

  - **XWINO**: 指代消解 (3题)
    - 基准分数: 0.891
    - 评测能力: 代词指代理解
    - 题目类型: 代词指代判断，语言理解

  - **MMLU**: 多学科理解 (4题)
    - 基准分数: 0.729
    - 评测能力: 57个学科综合知识
    - 题目类型: 学术选择题，跨学科知识

  - **GSM8K**: 小学数学 (4题)
    - 基准分数: 0.719
    - 评测能力: 基础数学应用
    - 题目类型: 数学应用题，逻辑推理

  - **MATH**: 竞赛数学 (3题)
    - 基准分数: 0.520
    - 评测能力: 高中数学竞赛
    - 题目类型: 高难度数学题，复杂计算

  - **BBH**: 困难推理 (2题)
    - 基准分数: 0.594
    - 评测能力: 复杂逻辑推理
    - 题目类型: 逻辑推理，抽象思维

  - **HumanEval**: 代码生成 (2题)
    - 基准分数: 0.617
    - 评测能力: 编程能力
    - 题目类型: Python函数编写，算法实现

#### 2.3 考试执行与评分
- **快速验证测试**
  - 运行quick_test.py进行快速验证
  - 测试3个核心数据集 (OpenBookQA, MMLU, GSM8K)
  - 验证评测系统正常工作
  - 记录初步性能数据

- **完整智商考试**
  - 运行qwen3_4b_evaluation.py完整评测
  - 执行全部10项智商测试
  - 自动生成Markdown格式报告
  - 记录详细的评测结果

- **评分标准与分析**
  - 计算各项智商指标得分
  - 对比基准分数，计算保持率
  - 生成综合智商评估报告
  - 识别优势和待改进领域
  - 为后续优化提供数据支撑

#### 2.4 服务集成测试
- **Flask服务与评测系统联调**
  - 通过API接口调用模型进行评测
  - 验证服务响应时间和准确性
  - 测试批量请求处理能力
  - 监控服务资源使用情况

- **性能基准建立**
  - 记录当前CPU模式下的性能表现
  - 建立首Token延迟基准
  - 记录Token生成速度基准
  - 测量内存占用和功耗基准
  - 为后续NPU优化提供对比数据

